<template>
  <div class="h-[50%] overflow-y-auto">
    <div class="grid grid-cols-2 xl:grid-cols-3 gap-3">
      <!-- 温度监测 -->
      <div class="bg-dark-card border border-dark-border/50 rounded-xl p-3 card-shadow">
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-medium flex items-center text-sm">
            <i class="fa fa-thermometer-half text-warning mr-2"></i>温度监测
          </h3>
          <span class="text-xs text-gray-400">TEMP-001</span>
        </div>
        <div class="flex items-end justify-between">
          <div>
            <div class="text-2xl font-bold">{{ deviceData.temperature }}°C</div>
            <div class="text-xs text-gray-400 mt-1">较昨日 <span class="text-green-500">↓0.3°C</span></div>
          </div>
          <div class="w-16 h-16">
            <v-chart :option="temperatureChartOption" autoresize class="w-full h-full" />
          </div>
        </div>
      </div>

      <!-- 湿度监测 -->
      <div class="bg-dark-card border border-dark-border/50 rounded-xl p-3 card-shadow">
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-medium flex items-center text-sm">
            <i class="fa fa-tint text-secondary mr-2"></i>湿度监测
          </h3>
          <span class="text-xs text-gray-400">HUM-001</span>
        </div>
        <div class="flex items-end justify-between">
          <div>
            <div class="text-2xl font-bold">{{ deviceData.humidity }}%</div>
            <div class="text-xs text-gray-400 mt-1">较昨日 <span class="text-red-500">↑5%</span></div>
          </div>
          <div class="w-16 h-16">
            <v-chart :option="humidityChartOption" autoresize class="w-full h-full" />
          </div>
        </div>
      </div>

      <!-- 风速监测 -->
      <div class="bg-dark-card border border-dark-border/50 rounded-xl p-3 card-shadow">
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-medium flex items-center text-sm">
            <i class="fa fa-wind text-primary mr-2"></i>风速监测
          </h3>
          <span class="text-xs text-gray-400">WIND-001</span>
        </div>
        <div class="flex items-end justify-between">
          <div>
            <div class="text-2xl font-bold">{{ deviceData.windSpeed }} m/s</div>
            <div class="text-xs text-gray-400 mt-1">风力等级: <span class="text-warning">7级</span></div>
          </div>
          <div class="w-16 h-16">
            <v-chart :option="windSpeedChartOption" autoresize class="w-full h-full" />
          </div>
        </div>
      </div>

      <!-- 风向监测 -->
      <div class="bg-dark-card border border-dark-border/50 rounded-xl p-3 card-shadow">
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-medium flex items-center text-sm">
            <i class="fa fa-compass text-purple-500 mr-2"></i>风向监测
          </h3>
          <span class="text-xs text-gray-400">WDIR-001</span>
        </div>
        <div class="flex flex-col items-center">
          <div class="w-16 h-16 relative mb-2">
            <div class="absolute inset-0 flex items-center justify-center">
              <i class="fa fa-compass text-3xl text-gray-500"></i>
            </div>
            <div class="absolute inset-0 flex items-center justify-center">
              <div
                class="w-1 h-8 bg-purple-500 rounded-t-full transform origin-bottom transition-transform duration-500"
                :style="{ transform: `rotate(${deviceData.windDirection}deg)` }"
              ></div>
            </div>
          </div>
          <div class="text-lg font-bold">{{ deviceData.windDirectionText }}</div>
          <div class="text-xs text-gray-400">{{ deviceData.windDirection }}°</div>
        </div>
      </div>

      <!-- 气压监测 -->
      <div class="bg-dark-card border border-dark-border/50 rounded-xl p-3 card-shadow">
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-medium flex items-center text-sm">
            <i class="fa fa-cloud text-gray-400 mr-2"></i>气压监测
          </h3>
          <span class="text-xs text-gray-400">PRESS-001</span>
        </div>
        <div class="flex items-end justify-between">
          <div>
            <div class="text-2xl font-bold">{{ deviceData.pressure }} hPa</div>
            <div class="text-xs text-gray-400 mt-1">趋势: <span class="text-red-500">↓0.5 hPa</span></div>
          </div>
          <div class="w-16 h-16">
            <v-chart :option="pressureChartOption" autoresize class="w-full h-full" />
          </div>
        </div>
      </div>

      <!-- 降雨量监测 -->
      <div class="bg-dark-card border border-dark-border/50 rounded-xl p-3 card-shadow">
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-medium flex items-center text-sm">
            <i class="fa fa-tint text-blue-400 mr-2"></i>降雨量监测
          </h3>
          <span class="text-xs text-gray-400">RAIN-001</span>
        </div>
        <div class="flex items-end justify-between">
          <div>
            <div class="text-2xl font-bold">{{ deviceData.rainfall }} mm</div>
            <div class="text-xs text-gray-400 mt-1">24小时累计</div>
          </div>
          <div class="w-16 h-16">
            <v-chart :option="rainfallChartOption" autoresize class="w-full h-full" />
          </div>
        </div>
      </div>

      <!-- 海浪高度监测 -->
      <div class="bg-dark-card border border-dark-border/50 rounded-xl p-3 card-shadow">
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-medium flex items-center text-sm">
            <i class="fa fa-water text-cyan-400 mr-2"></i>海浪高度
          </h3>
          <span class="text-xs text-gray-400">WAVE-001</span>
        </div>
        <div class="flex items-end justify-between">
          <div>
            <div class="text-2xl font-bold">{{ deviceData.waveHeight }} m</div>
            <div class="text-xs text-gray-400 mt-1">最大浪高: 4.2m</div>
          </div>
          <div class="w-16 h-16">
            <v-chart :option="waveChartOption" autoresize class="w-full h-full" />
          </div>
        </div>
      </div>

      <!-- 能见度监测 -->
      <div class="bg-dark-card border border-dark-border/50 rounded-xl p-3 card-shadow">
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-medium flex items-center text-sm">
            <i class="fa fa-eye text-yellow-400 mr-2"></i>能见度
          </h3>
          <span class="text-xs text-gray-400">VIS-001</span>
        </div>
        <div class="flex items-end justify-between">
          <div>
            <div class="text-2xl font-bold">{{ deviceData.visibility }} km</div>
            <div class="text-xs text-gray-400 mt-1">等级: 良好</div>
          </div>
          <div class="w-16 h-16">
            <v-chart :option="visibilityChartOption" autoresize class="w-full h-full" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import VChart from 'vue-echarts'
import 'echarts'

// Props
const props = defineProps({
  deviceData: {
    type: Object,
    required: true
  },
  temperatureChartOption: {
    type: Object,
    required: true
  },
  humidityChartOption: {
    type: Object,
    required: true
  },
  windSpeedChartOption: {
    type: Object,
    required: true
  },
  pressureChartOption: {
    type: Object,
    required: true
  },
  rainfallChartOption: {
    type: Object,
    required: true
  },
  waveChartOption: {
    type: Object,
    required: true
  },
  visibilityChartOption: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
/* Tailwind自定义样式 */
.bg-dark-card {
  background-color: #161B22;
}

.border-dark-border {
  border-color: #30363D;
}

.text-primary {
  color: #165DFF;
}

.text-secondary {
  color: #36BFFA;
}

.text-warning {
  color: #FF7D00;
}

.card-shadow {
  box-shadow: 0 0 15px rgba(22, 93, 255, 0.1);
}

/* 确保图表容器正确显示 */
.echarts {
  width: 100% !important;
  height: 100% !important;
}
</style>

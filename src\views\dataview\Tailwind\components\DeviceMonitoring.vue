<template>
  <div class="device-monitoring-container">
    <!-- 监测设备网格 -->
    <div class="monitoring-grid">
      <!-- 温度监测 -->
      <div class="device-card temperature-card">
        <div class="device-header">
          <div class="device-title">
            <div class="device-icon temperature-icon">
              <i class="fa fa-thermometer-half"></i>
            </div>
            <div class="device-info">
              <h3 class="device-name">温度监测</h3>
              <span class="device-id">TEMP-001</span>
            </div>
          </div>
          <div class="device-status online">
            <span class="status-dot"></span>
            <span>在线</span>
          </div>
        </div>

        <div class="device-content">
          <div class="device-data">
            <div class="data-value">{{ deviceData.temperature }}°C</div>
            <div class="data-trend">
              <span class="trend-label">较昨日</span>
              <span class="trend-value down">↓0.3°C</span>
            </div>
          </div>
          <div class="device-chart">
            <v-chart :option="temperatureChartOption" autoresize class="mini-chart" />
          </div>
        </div>

        <div class="device-decoration">
          <div class="decoration-line"></div>
        </div>
      </div>

      <!-- 湿度监测 -->
      <div class="device-card humidity-card">
        <div class="device-header">
          <div class="device-title">
            <div class="device-icon humidity-icon">
              <i class="fa fa-tint"></i>
            </div>
            <div class="device-info">
              <h3 class="device-name">湿度监测</h3>
              <span class="device-id">HUM-001</span>
            </div>
          </div>
          <div class="device-status online">
            <span class="status-dot"></span>
            <span>在线</span>
          </div>
        </div>

        <div class="device-content">
          <div class="device-data">
            <div class="data-value">{{ deviceData.humidity }}%</div>
            <div class="data-trend">
              <span class="trend-label">较昨日</span>
              <span class="trend-value up">↑5%</span>
            </div>
          </div>
          <div class="device-chart">
            <v-chart :option="humidityChartOption" autoresize class="mini-chart" />
          </div>
        </div>

        <div class="device-decoration">
          <div class="decoration-line"></div>
        </div>
      </div>

      <!-- 风速监测 -->
      <div class="device-card wind-speed-card">
        <div class="device-header">
          <div class="device-title">
            <div class="device-icon wind-speed-icon">
              <i class="fa fa-flag"></i>
            </div>
            <div class="device-info">
              <h3 class="device-name">风速监测</h3>
              <span class="device-id">WIND-001</span>
            </div>
          </div>
          <div class="device-status online">
            <span class="status-dot"></span>
            <span>在线</span>
          </div>
        </div>

        <div class="device-content">
          <div class="device-data">
            <div class="data-value">{{ deviceData.windSpeed }} m/s</div>
            <div class="data-trend">
              <span class="trend-label">风力等级</span>
              <span class="trend-value warning">7级</span>
            </div>
          </div>
          <div class="device-chart">
            <v-chart :option="windSpeedChartOption" autoresize class="mini-chart" />
          </div>
        </div>

        <div class="device-decoration">
          <div class="decoration-line"></div>
        </div>
      </div>

      <!-- 风向监测 -->
      <div class="device-card wind-direction-card">
        <div class="device-header">
          <div class="device-title">
            <div class="device-icon wind-direction-icon">
              <i class="fa fa-compass"></i>
            </div>
            <div class="device-info">
              <h3 class="device-name">风向监测</h3>
              <span class="device-id">WDIR-001</span>
            </div>
          </div>
          <div class="device-status online">
            <span class="status-dot"></span>
            <span>在线</span>
          </div>
        </div>

        <div class="device-content wind-direction-content">
          <div class="compass-container">
            <div class="compass-bg">
              <i class="fa fa-compass"></i>
            </div>
            <div class="compass-needle" :style="{ transform: `rotate(${deviceData.windDirection}deg)` }">
              <div class="needle"></div>
            </div>
          </div>
          <div class="wind-direction-data">
            <div class="direction-text">{{ deviceData.windDirectionText }}</div>
            <div class="direction-degree">{{ deviceData.windDirection }}°</div>
          </div>
        </div>

        <div class="device-decoration">
          <div class="decoration-line"></div>
        </div>
      </div>

      <!-- 气压监测 -->
      <div class="device-card pressure-card">
        <div class="device-header">
          <div class="device-title">
            <div class="device-icon pressure-icon">
              <i class="fa fa-cloud"></i>
            </div>
            <div class="device-info">
              <h3 class="device-name">气压监测</h3>
              <span class="device-id">PRESS-001</span>
            </div>
          </div>
          <div class="device-status online">
            <span class="status-dot"></span>
            <span>在线</span>
          </div>
        </div>

        <div class="device-content">
          <div class="device-data">
            <div class="data-value">{{ deviceData.pressure }} hPa</div>
            <div class="data-trend">
              <span class="trend-label">趋势</span>
              <span class="trend-value down">↓0.5 hPa</span>
            </div>
          </div>
          <div class="device-chart">
            <v-chart :option="pressureChartOption" autoresize class="mini-chart" />
          </div>
        </div>

        <div class="device-decoration">
          <div class="decoration-line"></div>
        </div>
      </div>

      <!-- 降雨量监测 -->
      <div class="device-card rainfall-card">
        <div class="device-header">
          <div class="device-title">
            <div class="device-icon rainfall-icon">
              <i class="fa fa-tint"></i>
            </div>
            <div class="device-info">
              <h3 class="device-name">降雨量监测</h3>
              <span class="device-id">RAIN-001</span>
            </div>
          </div>
          <div class="device-status online">
            <span class="status-dot"></span>
            <span>在线</span>
          </div>
        </div>

        <div class="device-content">
          <div class="device-data">
            <div class="data-value">{{ deviceData.rainfall }} mm</div>
            <div class="data-trend">
              <span class="trend-label">24小时累计</span>
            </div>
          </div>
          <div class="device-chart">
            <v-chart :option="rainfallChartOption" autoresize class="mini-chart" />
          </div>
        </div>

        <div class="device-decoration">
          <div class="decoration-line"></div>
        </div>
      </div>

      <!-- 海浪高度监测 -->
      <div class="device-card wave-card">
        <div class="device-header">
          <div class="device-title">
            <div class="device-icon wave-icon">
              <i class="fa fa-water"></i>
            </div>
            <div class="device-info">
              <h3 class="device-name">海浪高度</h3>
              <span class="device-id">WAVE-001</span>
            </div>
          </div>
          <div class="device-status online">
            <span class="status-dot"></span>
            <span>在线</span>
          </div>
        </div>

        <div class="device-content">
          <div class="device-data">
            <div class="data-value">{{ deviceData.waveHeight }} m</div>
            <div class="data-trend">
              <span class="trend-label">最大浪高</span>
              <span class="trend-value">4.2m</span>
            </div>
          </div>
          <div class="device-chart">
            <v-chart :option="waveChartOption" autoresize class="mini-chart" />
          </div>
        </div>

        <div class="device-decoration">
          <div class="decoration-line"></div>
        </div>
      </div>

      <!-- 能见度监测 -->
      <div class="device-card visibility-card">
        <div class="device-header">
          <div class="device-title">
            <div class="device-icon visibility-icon">
              <i class="fa fa-eye"></i>
            </div>
            <div class="device-info">
              <h3 class="device-name">能见度</h3>
              <span class="device-id">VIS-001</span>
            </div>
          </div>
          <div class="device-status online">
            <span class="status-dot"></span>
            <span>在线</span>
          </div>
        </div>

        <div class="device-content">
          <div class="device-data">
            <div class="data-value">{{ deviceData.visibility }} km</div>
            <div class="data-trend">
              <span class="trend-label">等级</span>
              <span class="trend-value good">良好</span>
            </div>
          </div>
          <div class="device-chart">
            <v-chart :option="visibilityChartOption" autoresize class="mini-chart" />
          </div>
        </div>

        <div class="device-decoration">
          <div class="decoration-line"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import VChart from 'vue-echarts'
import 'echarts'

// Props
const props = defineProps({
  deviceData: {
    type: Object,
    required: true
  },
  temperatureChartOption: {
    type: Object,
    required: true
  },
  humidityChartOption: {
    type: Object,
    required: true
  },
  windSpeedChartOption: {
    type: Object,
    required: true
  },
  pressureChartOption: {
    type: Object,
    required: true
  },
  rainfallChartOption: {
    type: Object,
    required: true
  },
  waveChartOption: {
    type: Object,
    required: true
  },
  visibilityChartOption: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
/* 设备监控容器 */
.device-monitoring-container {
  height: 100%;
  overflow-y: auto;
  padding-right: 5px;
}

/* 监测网格 */
.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

/* 设备卡片 */
.device-card {
  background: linear-gradient(135deg, rgba(22, 93, 255, 0.05), rgba(54, 191, 250, 0.05));
  border: 1px solid rgba(54, 191, 250, 0.2);
  border-radius: 12px;
  padding: 20px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.device-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(22, 93, 255, 0.2);
  border-color: rgba(54, 191, 250, 0.4);
}

.device-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(54, 191, 250, 0.6), transparent);
  opacity: 0.8;
}

/* 设备头部 */
.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.device-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.device-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #ffffff;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.device-name {
  font-size: 14px;
  font-weight: bold;
  color: #ffffff;
  margin: 0;
}

.device-id {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
}

.device-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.device-status.online {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #22C55E;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #22C55E;
  animation: pulse 2s infinite;
}

/* 设备内容 */
.device-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 15px;
}

.device-data {
  flex: 1;
}

.data-value {
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 5px;
  line-height: 1;
}

.data-trend {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.trend-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
}

.trend-value {
  font-size: 12px;
  font-weight: bold;
}

.trend-value.up {
  color: #EF4444;
}

.trend-value.down {
  color: #22C55E;
}

.trend-value.warning {
  color: #F59E0B;
}

.trend-value.good {
  color: #22C55E;
}

/* 设备图表 */
.device-chart {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.mini-chart {
  width: 100% !important;
  height: 100% !important;
}

/* 风向特殊样式 */
.wind-direction-content {
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.compass-container {
  position: relative;
  width: 60px;
  height: 60px;
}

.compass-bg {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: rgba(255, 255, 255, 0.3);
}

.compass-needle {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.5s ease;
}

.needle {
  width: 2px;
  height: 24px;
  background: linear-gradient(180deg, #A855F7, #9333EA);
  border-radius: 1px;
  transform: translateY(-12px);
}

.wind-direction-data {
  text-align: center;
}

.direction-text {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 2px;
}

.direction-degree {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* 装饰线条 */
.device-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  overflow: hidden;
}

.decoration-line {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(54, 191, 250, 0.6), transparent);
  animation: decorationScan 3s infinite;
}

/* 特定设备图标颜色 */
.temperature-icon {
  background: linear-gradient(135deg, #FF7D00, #F59E0B);
}

.humidity-icon {
  background: linear-gradient(135deg, #36BFFA, #0EA5E9);
}

.wind-speed-icon {
  background: linear-gradient(135deg, #165DFF, #3B82F6);
}

.wind-direction-icon {
  background: linear-gradient(135deg, #A855F7, #9333EA);
}

.pressure-icon {
  background: linear-gradient(135deg, #6B7280, #4B5563);
}

.rainfall-icon {
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
}

.wave-icon {
  background: linear-gradient(135deg, #06B6D4, #0891B2);
}

.visibility-icon {
  background: linear-gradient(135deg, #EAB308, #D97706);
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes decorationScan {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 滚动条样式 */
.device-monitoring-container::-webkit-scrollbar {
  width: 4px;
}

.device-monitoring-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.device-monitoring-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #165DFF, #36BFFA);
  border-radius: 2px;
}

.device-monitoring-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1e40af, #0284c7);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .monitoring-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
  }

  .device-card {
    padding: 15px;
  }

  .data-value {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .monitoring-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .device-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .device-chart {
    width: 100%;
    height: 40px;
  }

  .wind-direction-content {
    flex-direction: row;
    justify-content: space-between;
  }
}

/* 确保图表容器正确显示 */
.echarts {
  width: 100% !important;
  height: 100% !important;
}
</style>

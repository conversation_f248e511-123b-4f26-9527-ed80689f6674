<template>
  <div class="typhoon-list-container">
    <!-- 加载状态 -->
    <div v-if="stormList.length === 0" class="loading-state">
      <div class="loading-spinner">
        <i class="fa fa-spinner fa-spin"></i>
      </div>
      <div class="loading-text">正在获取台风数据...</div>
    </div>

    <!-- 台风列表 -->
    <div v-else class="storm-list">
      <div
        v-for="storm in stormList"
        :key="storm.id"
        class="storm-item"
        :class="{ 'storm-item-active': storm.id === selectedStormId }"
        @click="selectStorm(storm.id)"
      >
        <div class="storm-header">
          <div class="storm-name">{{ storm.name }}</div>
          <div class="storm-status">
            <span class="status-indicator"></span>
            <span>活跃</span>
          </div>
        </div>

        <div class="storm-info">
          <div class="storm-id">编号: {{ storm.id }}</div>
          <div class="storm-basin">海域: {{ getBasinName(storm.basin) }}</div>
        </div>

        <div v-if="storm.id === selectedStormId" class="selected-indicator">
          <i class="fa fa-check-circle"></i>
          <span>当前选中</span>
        </div>

        <!-- 科技感装饰线条 -->
        <div class="tech-lines">
          <div class="tech-line tech-line-1"></div>
          <div class="tech-line tech-line-2"></div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-footer">
      <div class="stats-item">
        <span class="stats-label">活跃台风</span>
        <span class="stats-value">{{ activeStormCount }}</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">监控海域</span>
        <span class="stats-value">西北太平洋</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  stormList: {
    type: Array,
    default: () => []
  },
  selectedStormId: {
    type: String,
    default: null
  }
})

// Emits
const emit = defineEmits(['select-storm'])

// Computed
const activeStormCount = computed(() => props.stormList.length)

// Methods
const selectStorm = (stormId) => {
  emit('select-storm', stormId)
}

// 获取海域名称
const getBasinName = (basin) => {
  const basinMap = {
    'NP': '西北太平洋',
    'EP': '东北太平洋',
    'WP': '西太平洋',
    'SP': '南太平洋',
    'NA': '北大西洋',
    'SA': '南大西洋',
    'NI': '北印度洋',
    'SI': '南印度洋'
  }
  return basinMap[basin] || basin || '未知'
}
</script>

<style scoped>
/* 台风列表容器 */
.typhoon-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 15px;
}

.loading-spinner {
  font-size: 24px;
  color: #165DFF;
}

.loading-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

/* 台风列表 */
.storm-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
}

/* 台风项目 */
.storm-item {
  background: linear-gradient(135deg, rgba(22, 93, 255, 0.05), rgba(54, 191, 250, 0.05));
  border: 1px solid rgba(54, 191, 250, 0.2);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.storm-item:hover {
  background: linear-gradient(135deg, rgba(22, 93, 255, 0.1), rgba(54, 191, 250, 0.1));
  border-color: rgba(54, 191, 250, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(22, 93, 255, 0.2);
}

.storm-item-active {
  background: linear-gradient(135deg, rgba(245, 63, 63, 0.1), rgba(255, 125, 0, 0.1));
  border-color: #F53F3F;
  box-shadow: 0 0 20px rgba(245, 63, 63, 0.3);
}

.storm-item-active:hover {
  background: linear-gradient(135deg, rgba(245, 63, 63, 0.15), rgba(255, 125, 0, 0.15));
}

/* 台风头部信息 */
.storm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.storm-name {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  letter-spacing: 0.02em;
}

.storm-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 10px;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 12px;
  font-size: 12px;
  color: #22C55E;
}

.status-indicator {
  width: 6px;
  height: 6px;
  background: #22C55E;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* 台风信息 */
.storm-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.storm-id, .storm-basin {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
}

/* 选中指示器 */
.selected-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(245, 63, 63, 0.1);
  border: 1px solid rgba(245, 63, 63, 0.3);
  border-radius: 8px;
  font-size: 12px;
  color: #F53F3F;
  margin-top: 10px;
}

.selected-indicator i {
  font-size: 14px;
}

/* 科技感装饰线条 */
.tech-lines {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.tech-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(54, 191, 250, 0.3), transparent);
  height: 1px;
}

.tech-line-1 {
  top: 30%;
  left: -100%;
  width: 200%;
  animation: techScan1 3s infinite;
}

.tech-line-2 {
  bottom: 30%;
  right: -100%;
  width: 200%;
  animation: techScan2 3s infinite 1.5s;
}

/* 统计信息 */
.stats-footer {
  display: flex;
  gap: 20px;
  padding: 15px 0;
  border-top: 1px solid rgba(54, 191, 250, 0.2);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  flex: 1;
}

.stats-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.stats-value {
  font-size: 18px;
  font-weight: bold;
  color: #165DFF;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes techScan1 {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes techScan2 {
  0% {
    right: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    right: 100%;
    opacity: 0;
  }
}

/* 滚动条样式 */
.storm-list::-webkit-scrollbar {
  width: 4px;
}

.storm-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.storm-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #165DFF, #36BFFA);
  border-radius: 2px;
}

.storm-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1e40af, #0284c7);
}
</style>

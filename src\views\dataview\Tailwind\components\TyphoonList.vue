<template>
  <div class="bg-dark-card/80 border border-dark-border/50 rounded-xl p-4 card-shadow h-full">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-lg font-semibold flex items-center">
        <i class="fa fa-list-ul text-primary mr-2"></i>活跃台风
      </h2>
      <span class="bg-danger/20 text-danger text-xs px-2 py-1 rounded-full">
        <i class="fa fa-exclamation-circle mr-1"></i>
        <span>{{ activeStormCount }}</span>个
      </span>
    </div>
    
    <div class="space-y-3 overflow-y-auto max-h-[calc(100vh-300px)]">
      <!-- 台风列表项 -->
      <div v-if="stormList.length === 0" class="flex items-center justify-center h-32 text-gray-400">
        <i class="fa fa-spinner fa-spin mr-2"></i>加载中...
      </div>
      <div v-else>
        <div
          v-for="storm in stormList"
          :key="storm.id"
          class="typhoon-item cursor-pointer p-3 rounded-lg transition-all duration-300 border"
          :class="storm.id === selectedStormId ? 'border-danger bg-danger/10' : 'border-dark-border/50 hover:bg-dark-card/50 hover:border-primary/50'"
          @click="selectStorm(storm.id)"
        >
          <div class="text-center">
            <div class="font-medium text-base mb-1">{{ storm.name }}</div>
            <div class="text-gray-400 text-sm">{{ storm.id }}</div>
            <div v-if="storm.id === selectedStormId" class="mt-2 text-xs text-danger">
              <i class="fa fa-check-circle mr-1"></i>已选中
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  stormList: {
    type: Array,
    default: () => []
  },
  selectedStormId: {
    type: String,
    default: null
  }
})

// Emits
const emit = defineEmits(['select-storm'])

// Computed
const activeStormCount = computed(() => props.stormList.length)

// Methods
const selectStorm = (stormId) => {
  emit('select-storm', stormId)
}
</script>

<style scoped>
/* Tailwind自定义样式 */
.bg-dark-card {
  background-color: #161B22;
}

.border-dark-border {
  border-color: #30363D;
}

.text-primary {
  color: #165DFF;
}

.text-danger {
  color: #F53F3F;
}

.card-shadow {
  box-shadow: 0 0 15px rgba(22, 93, 255, 0.1);
}

/* 动画效果 */
.typhoon-item {
  transition: all 0.3s ease;
}

.typhoon-item:hover {
  transform: translateY(-2px);
}
</style>

<template>
  <div v-show="selectedStormId" class="typhoon-details-container">
    <!-- 台风基本信息 -->
    <div class="storm-header-section">
      <div class="storm-title-area">
        <div class="storm-icon">
          <i class="fa fa-eye"></i>
        </div>
        <div class="storm-title-info">
          <h2 class="storm-title">{{ selectedStormName }}</h2>
          <div class="storm-subtitle">编号: {{ selectedStormId }}</div>
        </div>
        <div class="storm-status-badge">
          <span class="status-dot"></span>
          <span>活跃中</span>
        </div>
      </div>
    </div>

    <!-- 实时数据指标 -->
    <div class="metrics-section">
      <div class="section-title">
        <i class="fa fa-tachometer"></i>
        <span>实时监测数据</span>
      </div>

      <div class="metrics-grid">
        <div class="metric-card location-card">
          <div class="metric-icon">
            <i class="fa fa-map-marker"></i>
          </div>
          <div class="metric-content">
            <div class="metric-label">当前位置</div>
            <div class="metric-value">{{ currentLocation }}</div>
          </div>
          <div class="metric-decoration"></div>
        </div>

        <div class="metric-card type-card">
          <div class="metric-icon">
            <i class="fa fa-certificate"></i>
          </div>
          <div class="metric-content">
            <div class="metric-label">台风等级</div>
            <div class="metric-value">{{ typhoonType }}</div>
          </div>
          <div class="metric-decoration"></div>
        </div>

        <div class="metric-card pressure-card">
          <div class="metric-icon">
            <i class="fa fa-thermometer-half"></i>
          </div>
          <div class="metric-content">
            <div class="metric-label">中心气压</div>
            <div class="metric-value">{{ currentPressure }}</div>
          </div>
          <div class="metric-decoration"></div>
        </div>

        <div class="metric-card wind-card">
          <div class="metric-icon">
            <i class="fa fa-flag"></i>
          </div>
          <div class="metric-content">
            <div class="metric-label">最大风速</div>
            <div class="metric-value">{{ currentWindSpeed }}</div>
          </div>
          <div class="metric-decoration"></div>
        </div>
      </div>
    </div>

    <!-- 预测趋势 -->
    <div class="forecast-section">
      <div class="section-title">
        <i class="fa fa-line-chart"></i>
        <span>未来趋势预测</span>
      </div>

      <div class="forecast-timeline">
        <div class="forecast-item">
          <div class="forecast-time">
            <span class="time-label">12H</span>
            <span class="time-desc">12小时后</span>
          </div>
          <div class="forecast-content">
            <div class="forecast-data">
              <div class="data-item">
                <span class="data-label">位置</span>
                <span class="data-value">{{ forecast12h.location }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">风速</span>
                <span class="data-value">{{ forecast12h.wind }} m/s</span>
              </div>
            </div>
          </div>
          <div class="forecast-connector"></div>
        </div>

        <div class="forecast-item">
          <div class="forecast-time">
            <span class="time-label">24H</span>
            <span class="time-desc">24小时后</span>
          </div>
          <div class="forecast-content">
            <div class="forecast-data">
              <div class="data-item">
                <span class="data-label">位置</span>
                <span class="data-value">{{ forecast24h.location }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">风速</span>
                <span class="data-value">{{ forecast24h.wind }} m/s</span>
              </div>
            </div>
          </div>
          <div class="forecast-connector"></div>
        </div>

        <div class="forecast-item">
          <div class="forecast-time">
            <span class="time-label">48H</span>
            <span class="time-desc">48小时后</span>
          </div>
          <div class="forecast-content">
            <div class="forecast-data">
              <div class="data-item">
                <span class="data-label">位置</span>
                <span class="data-value">{{ forecast48h.location }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">风速</span>
                <span class="data-value">{{ forecast48h.wind }} m/s</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  selectedStormId: {
    type: String,
    default: null
  },
  selectedStormName: {
    type: String,
    default: '台风名称'
  },
  currentLocation: {
    type: String,
    default: '--'
  },
  typhoonType: {
    type: String,
    default: '--'
  },
  currentPressure: {
    type: String,
    default: '-- hPa'
  },
  currentWindSpeed: {
    type: String,
    default: '-- m/s'
  },
  forecast12h: {
    type: Object,
    default: () => ({ location: '--', wind: '--' })
  },
  forecast24h: {
    type: Object,
    default: () => ({ location: '--', wind: '--' })
  },
  forecast48h: {
    type: Object,
    default: () => ({ location: '--', wind: '--' })
  }
})

// Emits
const emit = defineEmits(['refresh-storm-data'])

// Methods
const refreshStormData = () => {
  emit('refresh-storm-data')
}
</script>

<style scoped>
/* 台风详情容器 */
.typhoon-details-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 台风头部信息 */
.storm-header-section {
  background: linear-gradient(135deg, rgba(245, 63, 63, 0.1), rgba(255, 125, 0, 0.1));
  border: 1px solid rgba(245, 63, 63, 0.3);
  border-radius: 8px;
  padding: 12px;
  position: relative;
  overflow: hidden;
}

.storm-header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #F53F3F, #FF7D00, transparent);
  opacity: 0.8;
}

.storm-title-area {
  display: flex;
  align-items: center;
  gap: 10px;
}

.storm-icon {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #F53F3F, #FF7D00);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(245, 63, 63, 0.3);
}

.storm-icon i {
  font-size: 18px;
  color: #ffffff;
}

.storm-title-info {
  flex: 1;
}

.storm-title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 3px;
  letter-spacing: 0.02em;
}

.storm-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.storm-status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(245, 63, 63, 0.1);
  border: 1px solid rgba(245, 63, 63, 0.3);
  border-radius: 16px;
  font-size: 12px;
  color: #F53F3F;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #F53F3F;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* 指标区域 */
.metrics-section {
  background: linear-gradient(135deg, rgba(22, 93, 255, 0.05), rgba(54, 191, 250, 0.05));
  border: 1px solid rgba(54, 191, 250, 0.2);
  border-radius: 12px;
  padding: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(54, 191, 250, 0.2);
}

.section-title i {
  color: #165DFF;
  font-size: 20px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

/* 指标卡片 */
.metric-card {
  background: linear-gradient(135deg, rgba(22, 93, 255, 0.1), rgba(54, 191, 250, 0.1));
  border: 1px solid rgba(54, 191, 250, 0.3);
  border-radius: 10px;
  padding: 20px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(22, 93, 255, 0.2);
  border-color: rgba(54, 191, 250, 0.5);
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 15px;
}

.metric-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #165DFF, #36BFFA);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.metric-icon i {
  font-size: 18px;
  color: #ffffff;
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 5px;
}

.metric-value {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

.metric-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(54, 191, 250, 0.1));
}

/* 特定卡片颜色 */
.location-card .metric-icon {
  background: linear-gradient(135deg, #22C55E, #16A34A);
}

.type-card .metric-icon {
  background: linear-gradient(135deg, #F59E0B, #D97706);
}

.pressure-card .metric-icon {
  background: linear-gradient(135deg, #A855F7, #9333EA);
}

.wind-card .metric-icon {
  background: linear-gradient(135deg, #EF4444, #DC2626);
}

/* 预测区域 */
.forecast-section {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.05), rgba(147, 51, 234, 0.05));
  border: 1px solid rgba(168, 85, 247, 0.2);
  border-radius: 12px;
  padding: 20px;
  flex: 1;
}

.forecast-timeline {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.forecast-item {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

.forecast-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  min-width: 80px;
}

.time-label {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #A855F7, #9333EA);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: #ffffff;
}

.time-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.forecast-content {
  flex: 1;
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(147, 51, 234, 0.1));
  border: 1px solid rgba(168, 85, 247, 0.3);
  border-radius: 8px;
  padding: 15px;
}

.forecast-data {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
}

.data-value {
  font-size: 14px;
  font-weight: bold;
  color: #ffffff;
}

.forecast-connector {
  position: absolute;
  left: 60px;
  top: 50px;
  width: 2px;
  height: 30px;
  background: linear-gradient(180deg, #A855F7, transparent);
}

.forecast-item:last-child .forecast-connector {
  display: none;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .storm-title-area {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .forecast-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .forecast-connector {
    display: none;
  }
}
</style>

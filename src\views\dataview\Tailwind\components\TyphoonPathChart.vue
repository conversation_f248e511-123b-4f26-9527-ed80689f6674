<template>
  <div class="typhoon-path-container">
    <!-- 图表区域 -->
    <div class="chart-area">
      <v-chart
        v-if="pathChartOption"
        :option="pathChartOption"
        :loading="chartLoading"
        autoresize
        class="path-chart"
      />
      <div v-else class="chart-loading">
        <div class="loading-content">
          <div class="loading-spinner">
            <i class="fa fa-spinner fa-spin"></i>
          </div>
          <div class="loading-text">正在加载台风路径数据...</div>
        </div>
      </div>
    </div>

    <!-- 图表控制栏 -->
    <div class="chart-controls">
      <div class="control-group">
        <button class="control-btn" @click="zoomIn">
          <i class="fa fa-search-plus"></i>
          <span>放大</span>
        </button>
        <button class="control-btn" @click="zoomOut">
          <i class="fa fa-search-minus"></i>
          <span>缩小</span>
        </button>
        <button class="control-btn" @click="resetZoom">
          <i class="fa fa-refresh"></i>
          <span>重置</span>
        </button>
      </div>

      <div class="chart-info">
        <div class="info-item">
          <span class="info-label">更新时间</span>
          <span class="info-value">{{ updateTime }}</span>
        </div>
      </div>
    </div>

    <!-- 科技感装饰 -->
    <div class="tech-decorations">
      <div class="corner-decoration top-left"></div>
      <div class="corner-decoration top-right"></div>
      <div class="corner-decoration bottom-left"></div>
      <div class="corner-decoration bottom-right"></div>
      <div class="scan-line"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import VChart from 'vue-echarts'
import 'echarts'

// Props
const props = defineProps({
  pathChartOption: {
    type: Object,
    default: null
  },
  chartLoading: {
    type: Boolean,
    default: false
  }
})

// 响应式数据
const chartRef = ref(null)

// 计算属性
const updateTime = computed(() => {
  const now = new Date()
  return now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  })
})

// 方法
const zoomIn = () => {
  if (chartRef.value) {
    chartRef.value.dispatchAction({
      type: 'dataZoom',
      start: 20,
      end: 80
    })
  }
}

const zoomOut = () => {
  if (chartRef.value) {
    chartRef.value.dispatchAction({
      type: 'dataZoom',
      start: 0,
      end: 100
    })
  }
}

const resetZoom = () => {
  if (chartRef.value) {
    chartRef.value.dispatchAction({
      type: 'restore'
    })
  }
}
</script>

<style scoped>
/* 台风路径容器 */
.typhoon-path-container {
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.05), rgba(14, 165, 233, 0.05));
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 12px;
  overflow: hidden;
}

/* 图表区域 */
.chart-area {
  height: calc(100% - 60px);
  position: relative;
  padding: 20px;
}

.path-chart {
  width: 100% !important;
  height: 100% !important;
  border-radius: 8px;
}

/* 加载状态 */
.chart-loading {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(14, 165, 233, 0.1));
  border-radius: 8px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading-spinner {
  font-size: 32px;
  color: #06B6D4;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

/* 图表控制栏 */
.chart-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(14, 165, 233, 0.1));
  border-top: 1px solid rgba(6, 182, 212, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.control-group {
  display: flex;
  gap: 10px;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(6, 182, 212, 0.1);
  border: 1px solid rgba(6, 182, 212, 0.3);
  border-radius: 6px;
  color: #06B6D4;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(6, 182, 212, 0.2);
  border-color: rgba(6, 182, 212, 0.5);
  transform: translateY(-1px);
}

.control-btn i {
  font-size: 14px;
}

.chart-info {
  display: flex;
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.info-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
}

.info-value {
  font-size: 13px;
  color: #06B6D4;
  font-weight: bold;
}

/* 科技感装饰 */
.tech-decorations {
  position: absolute;
  inset: 0;
  pointer-events: none;
  overflow: hidden;
}

.corner-decoration {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(6, 182, 212, 0.5);
}

.corner-decoration.top-left {
  top: 10px;
  left: 10px;
  border-right: none;
  border-bottom: none;
}

.corner-decoration.top-right {
  top: 10px;
  right: 10px;
  border-left: none;
  border-bottom: none;
}

.corner-decoration.bottom-left {
  bottom: 70px;
  left: 10px;
  border-right: none;
  border-top: none;
}

.corner-decoration.bottom-right {
  bottom: 70px;
  right: 10px;
  border-left: none;
  border-top: none;
}

.scan-line {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #06B6D4, transparent);
  animation: scanLine 4s infinite;
}

/* 动画效果 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes scanLine {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    height: 80px;
    gap: 10px;
    padding: 10px;
  }

  .control-group {
    gap: 5px;
  }

  .control-btn {
    padding: 6px 10px;
    font-size: 11px;
  }

  .chart-area {
    height: calc(100% - 80px);
    padding: 15px;
  }

  .corner-decoration.bottom-left,
  .corner-decoration.bottom-right {
    bottom: 90px;
  }
}

/* 确保图表容器正确显示 */
.echarts {
  width: 100% !important;
  height: 100% !important;
}
</style>
